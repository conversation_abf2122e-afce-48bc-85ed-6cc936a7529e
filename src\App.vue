<template>
  <div id="app">
    <h1>我的第一个Vue应用</h1>

    <!-- 计数器部分 -->
    <div class="counter-section">
      <h2>计数器</h2>
      <p>当前计数: {{ count }}</p>
      <button @click="increment">+1</button>
      <button @click="decrement">-1</button>
      <button @click="reset">重置</button>
    </div>

    <!-- 文本输入部分 -->
    <div class="input-section">
      <h2>文本输入</h2>
      <input v-model="message" placeholder="请输入一些文字..." />
      <p>你输入的内容: {{ message }}</p>
    </div>

    <!-- 待办事项列表 -->
    <div class="todo-section">
      <h2>简单待办列表</h2>
      <div class="add-todo">
        <input v-model="newTodo" placeholder="添加新任务..." @keyup.enter="addTodo" />
        <button @click="addTodo">添加</button>
      </div>
      <ul>
        <li v-for="(todo, index) in todos" :key="index" class="todo-item">
          <span :class="{ completed: todo.completed }" @click="toggleTodo(index)">
            {{ todo.text }}
          </span>
          <button @click="removeTodo(index)" class="delete-btn">删除</button>
        </li>
      </ul>
    </div>

    <!-- 条件渲染 -->
    <div class="conditional-section">
      <h2>条件渲染</h2>
      <button @click="showMessage = !showMessage">
        {{ showMessage ? '隐藏' : '显示' }}消息
      </button>
      <p v-if="showMessage" class="message">这是一条可以显示/隐藏的消息！</p>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 计数器
const count = ref(0)
const increment = () => count.value++
const decrement = () => count.value--
const reset = () => count.value = 0

// 文本输入
const message = ref('')

// 待办事项
const newTodo = ref('')
const todos = ref([
  { text: '学习Vue基础', completed: false },
  { text: '创建第一个组件', completed: true },
  { text: '理解响应式数据', completed: false }
])

const addTodo = () => {
  if (newTodo.value.trim()) {
    todos.value.push({
      text: newTodo.value,
      completed: false
    })
    newTodo.value = ''
  }
}

const removeTodo = (index) => {
  todos.value.splice(index, 1)
}

const toggleTodo = (index) => {
  todos.value[index].completed = !todos.value[index].completed
}

// 条件渲染
const showMessage = ref(true)
</script>

<style scoped>
#app {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

h1 {
  color: #42b883;
  text-align: center;
}

h2 {
  color: #35495e;
  border-bottom: 2px solid #42b883;
  padding-bottom: 5px;
}

.counter-section,
.input-section,
.todo-section,
.conditional-section {
  margin: 30px 0;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #f9f9f9;
}

button {
  background-color: #42b883;
  color: white;
  border: none;
  padding: 8px 16px;
  margin: 5px;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background-color: #369870;
}

input {
  padding: 8px;
  margin: 5px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.todo-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  margin: 5px 0;
  background-color: white;
  border-radius: 4px;
}

.todo-item span {
  cursor: pointer;
  flex-grow: 1;
}

.completed {
  text-decoration: line-through;
  color: #888;
}

.delete-btn {
  background-color: #e74c3c;
  font-size: 12px;
  padding: 4px 8px;
}

.delete-btn:hover {
  background-color: #c0392b;
}

.add-todo {
  display: flex;
  margin-bottom: 15px;
}

.add-todo input {
  flex-grow: 1;
}

.message {
  background-color: #d4edda;
  color: #155724;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #c3e6cb;
}

ul {
  list-style: none;
  padding: 0;
}
</style>
